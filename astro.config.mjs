// @ts-check
import { defineConfig } from 'astro/config';

import netlify from '@astrojs/netlify';

import tailwind from '@astrojs/tailwind';

import icon from 'astro-icon';

import sitemap from '@astrojs/sitemap';

import robotsTxt from 'astro-robots-txt';

import partytown from '@astrojs/partytown';

// https://astro.build/config
export default defineConfig({
  adapter: netlify(),
  site: 'https://trytimeout.com',
  integrations: [
    tailwind(),
    icon(), 
    sitemap(), 
    robotsTxt(), 
    partytown({
      config: {
        forward: ['dataLayer.push']
      }
    })
  ]
});
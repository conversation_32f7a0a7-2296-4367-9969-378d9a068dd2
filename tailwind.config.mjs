/** @type {import('tailwindcss').Config} */
export default {
	content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
	theme: {
		extend: {
			fontFamily: {
				inter: ['Inter', 'sans-serif'],
			},
			colors: {
				primary: {
					50: '#f8faff',
					100: '#f0f5ff',
					200: '#e0e7fe', // New primary base color
					300: '#c7d2fe',
					400: '#a5b4fc',
					500: '#818cf8',
					600: '#6366f1',
					700: '#4f46e5',
					800: '#4338ca',
					900: '#3730a3',
				},
				secondary: {
					50: '#f5f3ff',
					100: '#ede9fe',
					200: '#ddd6fe',
					300: '#c4b5fd',
					400: '#a78bfa',
					500: '#8b5cf6',
					600: '#808cf5', // New secondary base color
					700: '#6d28d9',
					800: '#5b21b6',
					900: '#4c1d95',
				},
			},
		},
	},
	plugins: [],
}

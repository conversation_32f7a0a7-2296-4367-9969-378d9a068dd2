---
import { Image } from 'astro:assets';
import heroImage from '../assets/hero-image.png';
---

<section id="hero-header" class="pt-32 pb-20 md:pt-40 md:pb-32">
  <div class="container mx-auto px-4">
    <div class="flex flex-col md:flex-row items-center">
      <div class="md:w-1/2 mb-10 md:mb-0">
        <div class="max-w-lg">
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span class="bg-gradient-to-r from-primary-600 to-secondary-600 text-transparent bg-clip-text">
              Scroll Less
            </span>
            <br />
            <span>Move More</span>
          </h1>
          <p class="text-lg text-gray-700 mb-8">
            Time Out lets you manage your screen time by locking apps behind exercise goals. So no more doom scrolling until you've hit 10,000 steps!
          </p>
          <div class="flex flex-col sm:flex-row gap-4">
            <a href="https://apps.apple.com/ie/app/time-out-lock-apps-for-focus/id6738120947" class="bg-gradient-to-r from-primary-600 to-secondary-600 text-white px-8 py-3 rounded-full font-medium text-center hover:shadow-lg transition-all">
              Download Free
            </a>
            <a href="#features" class="border border-gray-300 text-gray-700 px-8 py-3 rounded-full font-medium text-center hover:border-primary-600 hover:text-primary-600 transition-all">
              Learn More
            </a>
          </div>
        </div>
      </div>
      <div class="md:w-1/2">
        <div class="relative">
          <div class="relative">
            <img 
                src={heroImage.src} 
                alt="TimeOut App Interface" 
                class="w-full h-auto"
              />
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

---
---

<section id="features" class="py-20 bg-white">
  <div class="container mx-auto px-4">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-bold mb-4">Powerful Features</h2>
      <p class="text-lg text-gray-700 max-w-2xl mx-auto">
        Time Out comes packed with features designed to boost your productivity and help you manage your time effectively.
      </p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Feature 1 -->
      <div class="bg-gray-50 rounded-xl p-6 hover:shadow-md transition-all">
        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" width="24" height="24" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="M12 4a1 1 0 1 0 2 0a1 1 0 1 0-2 0M4 17l5 1l.75-1.5M15 21v-4l-4-3l1-6"/><path d="M7 12V9l5-1l3 3l3 1"/></g></svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">Exercise Locks</h3>
        <p class="text-gray-700">
          Time Out lets you set App locks that unlock when you've hit your desired exercise goal.
        </p>
      </div>
      
      <!-- Feature 2 -->
      <div class="bg-gray-50 rounded-xl p-6 hover:shadow-md transition-all">
        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600"width="24" height="24" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="M10.5 21H6a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v3m-4-7v4M8 3v4m-4 4h10"/><path d="M14 18a4 4 0 1 0 8 0a4 4 0 1 0-8 0"/><path d="M18 16.5V18l.5.5"/></g></svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">Scheduled Locks</h3>
        <p class="text-gray-700">
          Create scheduled locks to block access to distracting apps at times when it matters most.
        </p>
      </div>
      
      <!-- Feature 3 -->
      <div class="bg-gray-50 rounded-xl p-6 hover:shadow-md transition-all">
        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" width="24" height="24" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.56 3.69a9 9 0 0 0-2.92 1.95M3.69 8.56A9 9 0 0 0 3 12m.69 3.44a9 9 0 0 0 1.95 2.92m2.92 1.95A9 9 0 0 0 12 21m3.44-.69a9 9 0 0 0 2.92-1.95m1.95-2.92A9 9 0 0 0 21 12m-.69-3.44a9 9 0 0 0-1.95-2.92m-2.92-1.95A9 9 0 0 0 12 3m-2 7l2-2v8"/></svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">One-Off Locks</h3>
        <p class="text-gray-700">
          Trying to focus on a task? Set a one-off lock to block access to distracting apps for a specific duration.
        </p>
      </div>
      
      <!-- Feature 4 -->
      <div class="bg-gray-50 rounded-xl p-6 hover:shadow-md transition-all">
        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" width="24" height="24" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 12a1 1 0 1 0 2 0a1 1 0 1 0-2 0M4 8V6a2 2 0 0 1 2-2h2M4 16v2a2 2 0 0 0 2 2h2m8-16h2a2 2 0 0 1 2 2v2m-4 12h2a2 2 0 0 0 2-2v-2"/></svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">Focus Integration</h3>
        <p class="text-gray-700">
          Integrate Time Out's App locks with your existing iOS focus modes.
        </p>
      </div>
      
      <!-- Feature 5 -->
      <div class="bg-gray-50 rounded-xl p-6 hover:shadow-md transition-all">
        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" width="24" height="24" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="M14 3v4a1 1 0 0 0 1 1h4"/><path d="M17 21H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7l5 5v11a2 2 0 0 1-2 2"/></g></svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">Reports</h3>
        <p class="text-gray-700">
          Check out how you're doing compared to yesterday and beat your previous screen time low.
        </p>
      </div>
      
      <!-- Feature 6 -->
      <div class="bg-gray-50 rounded-xl p-6 hover:shadow-md transition-all">
        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" width="24" height="24" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5h8m-8 4h5m-5 6h8m-8 4h5M3 5a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1zm0 10a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1z"/></svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">Block Lists</h3>
        <p class="text-gray-700">
          Want to use the same set of apps for different locks? Easily group them together with Block Lists.
        </p>
      </div>
    </div>

  </div>
</section>

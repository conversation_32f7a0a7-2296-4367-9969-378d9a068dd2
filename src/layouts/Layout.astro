---
interface Props {
	title?: string;
	description?: string;
}

const { title = "Time Out App - Lock apps behind exercise goals", description = "Scroll less. Move more. Boost your productivity with Time Out." } = Astro.props;
---

<!doctype html>
<html lang="en" class="scroll-smooth">
	<head>
		<!-- Google tag (gtag.js) -->
		<script type="text/partytown" async src="https://www.googletagmanager.com/gtag/js?id=G-VS773GT4ZY"></script>
		<script type="text/partytown">
			window.dataLayer = window.dataLayer || [];
			function gtag(){dataLayer.push(arguments);}
			gtag('js', new Date());

			gtag('config', 'G-VS773GT4ZY');
		</script>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width" />
		<link rel="icon" type="image/svg+xml" href="/favicon.ico" />
		<link rel="sitemap" href="/sitemap-index.xml" />
		<meta name="title" content={title}>
		<meta name="generator" content={Astro.generator} />
		<meta name="description" content={description} />
		<meta property="og:title" content={title} />
		<meta property="og:image" content="https://trytimeout.com/ogIndex.png" />
		<meta property="og:description" content={description} />
		<meta property="og:url" content="https://trytimeout.com"} />
		<meta property="og:type" content="website" />
		<meta property="og:site_name" content="Time Out. Scroll less move more." />
		<meta property="og:locale" content="en_US.utf-8" />
		<title>{title}</title>
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
	</head>
	<body class="font-inter text-gray-900 bg-gradient-to-br from-blue-50 to-purple-50">
		<slot />
	</body>
</html>

<style is:global>
	html,
	body {
		margin: 0;
		width: 100%;
		min-height: 100%;
	}

	@font-face {
		font-family: 'Inter';
		src: url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
		font-display: swap;
	}
</style>
